<?php
ob_start();
session_start();
if (isset($_SESSION['confirmation_completed']) && $_SESSION['confirmation_completed'] === true) {
  header("Location: index.php");
  exit;
}
?>
<?php
if (isset($_GET['id'])) {
  $id = $_GET['id'];

  include('Connecte.php');



  $query = "SELECT * FROM products WHERE ProductID=$id";
  $result = mysqli_query($conn, $query);
  if ($row = mysqli_fetch_assoc($result)) {
    $n = $row["ProductName"];
    $price = $row["Price"];
    $d1 = $row["Description_1"];
    $d2 = $row["Description_2"];
    $Picture = $row['Picture'];
  }

  $queryx = "SELECT * FROM product_pictures WHERE ProductID=$id";
  $resultx = mysqli_query($conn, $queryx);

  $Pictures = array();

  if (mysqli_num_rows($resultx) > 0) {
    while ($rowx = mysqli_fetch_assoc($resultx)) {
      $Pictures[] = $rowx['Picture'];
    }
  }
}

$total = 0;
$date = date('Y-m-d');


$query_Reviews = "SELECT * FROM Reviews";
$result_Reviews = mysqli_query($conn, $query_Reviews);


?>
<!DOCTYPE html>
<html lang="fr">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" href="icon.gif" type="image/x-icon">
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200..1000&display=swap" rel="stylesheet" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <link rel="stylesheet" href="hide.css" />
  <link rel="stylesheet" href="all.min.css" />
  <title>Produit</title>
</head>
<style>
        .title {
        font-weight: bold;
    display: block;
    margin-bottom: 30px;
    font-size: 25px;
        }
        .description {
            font-weight: normal;
            display: block;
            margin-bottom: 23px;
        }
    </style>
<body>
  <?php
  include("Connecte.php");
  $sqlpixel = "SELECT script FROM facebook_pixel ";
  $resultpixel = $conn->query($sqlpixel);

  if ($resultpixel->num_rows > 0) {
    $rowpixel = $resultpixel->fetch_assoc();
    $pixel_id = $rowpixel['script'];
  }
  ?>
  <script>
    ! function(f, b, e, v, n, t, s) {
      if (f.fbq) return;
      n = f.fbq = function() {
        n.callMethod ?
          n.callMethod.apply(n, arguments) : n.queue.push(arguments)
      };
      if (!f._fbq) f._fbq = n;
      n.push = n;
      n.loaded = !0;
      n.version = '2.0';
      n.queue = [];
      t = b.createElement(e);
      t.async = !0;
      t.src = v;
      s = b.getElementsByTagName(e)[0];
      s.parentNode.insertBefore(t, s)
    }(window, document, 'script',
      'https://connect.facebook.net/en_US/fbevents.js');
    fbq('init', '<?php echo $pixel_id; ?>');
    fbq('track', 'PageView');
  </script>
  <noscript>
    <img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=<?php echo $pixel_id; ?>&ev=PageView&noscript=1" />
  </noscript>
  <div class="sidebar">
    <div class="" id="exit">✖</div>
    <div class="links">
      <ul>
        <a href="index.php">
          <li>Acceuil</li>
        </a>
        <a href="store.php">
          <li>Boutique</li>
        </a>
        <a href="livraison.php">
          <li>livraison</li>
        </a>
        <a href="paiment.php">
          <li>Payement</li>
        </a>
        <a href="Contact.php">
          <li>Contact</li>
        </a>
        <a href="about.php">
          <li>à Propos</li>
        </a>

      </ul>
    </div>
  </div>
  <div class="Bar">التوصيل متوفر</div>
  <div class="navbar">
    <div class="right"><i class="fa-solid fa-bars"></i></div>
    <a href="index.php">
      <div class="meduim"></div>
    </a>
    <div class="left"><i class="fa-solid fa-magnifying-glass"></i></div>
  </div>



  <div class="container" id="slide">
  <div class="product_dz">
          <h2><?php echo $n ?></h2>
        </div>
    <div class="pic" id="main-pic" style="background-image: url('data:image/jpeg;base64,<?php echo base64_encode($Picture); ?>');"></div>


    <div class="pic_product" id="slider">
      <?php
      if (!empty($Pictures)) {
        foreach ($Pictures as $picture) {
          echo '<div class="pic" style="background-image: url(data:image/jpeg;base64,' . base64_encode($picture) . ')"></div>';
        }
      }
      ?>
    </div>

    <button id="prev" class="slider-btn"><-</button>
    <button id="next" class="slider-btn">-></button>
    <div class="product_dz">
          <p> دج <?php echo $price ?></p>
        </div>
      
  </div>

  <div class="pic_product">
    <h2>    : إِملأ الاستمارة التالية لطلب المنتج </h2>
  </div>

  <div class="form">
    <form action="https://script.google.com/macros/s/AKfycbxbWsmQuVGDcveynEsoHyJHmD1Ri1tt6WXZVhsVZFpEGrQsnFtsRk1A1YlP7YP4Eqj3/exec" method="post" id="deliveryForm">
      <input type="text" placeholder="الاسم الكامل" name="name" required oninput="validateInput()" required />

      <input type="number" id="phoneNumber" placeholder="رقم الهاتف" name="number" pattern="^0[5-7][0-9]{8}$" maxlength="10" minlength="10" required onblur="validateInput()" />
      <select name="wilayas" id="wilayas" onclick="updateCommunes()">
        <option value="" hidden>الولاية</option>
        <option value="1">أدرار</option>
        <option value="2">الشلف</option>
        <option value="3">الأغواط</option>
        <option value="4">أم البواقي</option>
        <option value="5">باتنة</option>
        <option value="6">بجاية</option>
        <option value="7">بسكرة</option>
        <option value="8">بشار</option>
        <option value="9">البليدة</option>
        <option value="10">البويرة</option>
        <option value="11">تمنراست</option>
        <option value="12">تبسة</option>
        <option value="13">تلمسان</option>
        <option value="14">تيارت</option>
        <option value="15">تيزي وزو</option>
        <option value="16">الجزائر</option>
        <option value="17">الجلفة</option>
        <option value="18">جيجل</option>
        <option value="19">سطيف</option>
        <option value="20">سعيدة</option>
        <option value="21">سكيكدة</option>
        <option value="22">سيدي بلعباس</option>
        <option value="23">عنابة</option>
        <option value="24">قالمة</option>
        <option value="25">قسنطينة</option>
        <option value="26">المدية</option>
        <option value="27">مستغانم</option>
        <option value="28">المسيلة</option>
        <option value="29">معسكر</option>
        <option value="30">ورقلة</option>
        <option value="31">وهران</option>
        <option value="32">البيض</option>
        <option value="33">إليزي</option>
        <option value="34">برج بوعريريج</option>
        <option value="35">بومرداس</option>
        <option value="36">الطارف</option>
        <option value="37">تندوف</option>
        <option value="38">تيسمسيلت</option>
        <option value="39">الوادي</option>
        <option value="40">خنشلة</option>
        <option value="41">سوق أهراس</option>
        <option value="42">تيبازة</option>
        <option value="43">ميلة</option>
        <option value="44">عين الدفلى</option>
        <option value="45">النعامة</option>
        <option value="46">عين تيموشنت</option>
        <option value="47">غرداية</option>
        <option value="48">غليزان</option>
        <option value="49">تيميمون</option>
        <option value="51">أولاد جلال</option>
        <option value="52">بني عباس</option>
        <option value="53">عين صالح</option>
        <option value="55">تقرت</option>
        <option value="56">جانت</option>
        <option value="57">المغير</option>
        <option value="58">المنيعة</option>


      </select>

      <select name="commune" id="commune" required>
        <option value="" hidden>البلدية</option>
      </select>

      <input type="text" placeholder="العنوان الكامل" name="adresse" id="adresse"  />
      <select name="Quantite" id="Quantite" required>
        <option value="" hidden>الكمية</option>
        <option value="1">1</option>
        <option value="2">2</option>
        <option value="3">3</option>
        <option value="4">4</option>
        <option value="5">5</option>
        <option value="6">6</option>
        <option value="7">7</option>
        <option value="8">8</option>
        <option value="9">9</option>
        <option value="10">+10</option>
      </select>

      <select name="deliveryType" id="deliveryType" required>
      <option value="" hidden>التوصيل</option>
        <option value="desk" selected>الى المكتب</option>
        <option value="domicile">الى المنزل</option>
      </select>
  </div>




  <div class="details">
    <div>
      <p id="productPrice"><?php echo $price; ?> دج</p>
      <p>: ثمن المنتج </p>
      <input type="hidden" name="Prix" id="productPriceInput" value="<?php echo $price; ?>">
    </div>
    <div>
      <p id="livraisonPrice">...</p>
      <p>: ثمن التوصيل </p>
      <input type="hidden" name="Livraison" id="livraisonPriceInput" value="">
    </div>
    <div>
      <p><mark id="totalAmount"><?php echo $total; ?> دج</mark></p>
      <p>: الاجمالي </p>
      <input type="hidden" name="Total" id="TotalInput" value="">
    </div>
  </div>
  <input type="hidden" name="Date" id="Date" value="<?php echo ($date); ?>" />
  <input type="hidden" name="product" id="product" value="<?php echo ($n); ?>" />
  <input type="hidden" name="Type" id="TypeInput" value="" />
  <input type="hidden" name="Livraison" id="Livraison" value="" />
  <input type="hidden" name="Quantite" id="QuantiteInput" value="" />

  <div class="valider">
    <button type="submit" id="valider" name="valider">
      <span id="validerText">شراء</span>
      <span id="validerLoading" style="display:none; vertical-align:middle;">
        <i class="fa fa-spinner fa-spin" style="font-size:18px;"></i> شراء 
      </span>
    </button>
  </div>
  </form>


  <div class="about_us">
  <?php
        function formatText($text) {
            $formattedContent = '';
            $entries = explode('##', $text);

            foreach ($entries as $entry) {
                $parts = explode('::', $entry);
                if (count($parts) == 2) {
                    $title = nl2br(htmlspecialchars(trim($parts[0])));
                    $description = nl2br(htmlspecialchars(trim($parts[1])));
                    
                    // Replace "@" with <br> tags
                    $title = str_replace('@', '<br>', $title);
                    $description = str_replace('@', '<br>', $description);
                    
                    $formattedContent .= '<div class="title">' . $title . '</div>';
                    $formattedContent .= '<div class="description">' . $description . '</div>';
                }
            }
            return $formattedContent;
        }


        echo formatText($d1);
        ?>
  </div>
  <br><br>
  <div class="pic_product">
    <h2>    : اراء الزبائن</h2>
  </div>
  <div class="reviews">
        <button class="nav-button" id="prevBtn">←</button>
        <div class="review-content" id="reviewContent">
          <?php
          if ($result_Reviews && mysqli_num_rows($result_Reviews) > 0) {
            $index = 0;
            while ($row = mysqli_fetch_assoc($result_Reviews)) {
              $imageData = $row['Picture']; // Binary image data
              $imageBase64 = base64_encode($imageData); // Encode image data to base64
              echo '<div class="review" data-index="' . $index . '">';
              echo '<img src="data:image/jpeg;base64,' . htmlspecialchars($imageBase64, ENT_QUOTES, 'UTF-8') . '" />';
              echo '</div>';
              $index++;
            }
          }
          ?>
        </div>
        <button class="nav-button" id="nextBtn">→</button>
      </div>
  <br><br>

  <script>
    document.addEventListener('DOMContentLoaded', () => {
      const wilayaSelect = document.getElementById('wilayas');
      const quantitySelect = document.getElementById('Quantite');
      const deliveryTypeSelect = document.getElementById('deliveryType');
      const livraisonPrice = document.getElementById('livraisonPrice');
      const totalAmount = document.getElementById('totalAmount');
      const productPriceInput = document.getElementById('productPriceInput');
      const livraisonPriceInput = document.getElementById('livraisonPriceInput');
      const livraisonHiddenInput = document.getElementById('Livraison');
      const quantityHiddenInput = document.getElementById('QuantiteInput');
      const totalHiddenInput = document.getElementById('TotalInput');
      const typeHiddenInput = document.getElementById('TypeInput');
      const submitButton = document.getElementById('valider');

      // Check if all elements are correctly referenced
      const elements = {
        wilayaSelect,
        quantitySelect,
        livraisonPrice,
        totalAmount,
        productPriceInput,
        livraisonPriceInput,
        livraisonHiddenInput,
        quantityHiddenInput,
        totalHiddenInput,
        typeHiddenInput,
        submitButton
      };
      Object.entries(elements).forEach(([name, element]) => {
        if (!element) {
          console.error(`${name} is missing from the DOM.`);
        } else {
          console.log(`${name} is correctly referenced.`);
        }
      });

      wilayaSelect.addEventListener('change', updatePrice);
      quantitySelect.addEventListener('change', updatePrice);
      deliveryTypeSelect.addEventListener('change', updatePrice);

      function updatePrice() {
        const wilayaCode = wilayaSelect.value;
        const quantity = quantitySelect.value;
        const deliveryType = deliveryTypeSelect.value;
        const productPrice = parseFloat(productPriceInput.value);

        if (wilayaCode && quantity && deliveryType) {
          const jsonFile = deliveryType === 'desk' ? 'Desk.json' : 'Domicile.json';

          fetch(jsonFile)
            .then(response => response.json())
            .then(data => {
              let deliveryCost = null;
              for (const [cost, wilayas] of Object.entries(data)) {
                if (wilayas.includes(wilayaCode)) {
                  deliveryCost = cost;
                  break;
                }
              }

              const totalProductPrice = productPrice * quantity;
              const totalCost = deliveryCost ? (totalProductPrice + parseInt(deliveryCost)) : totalProductPrice;

              livraisonPrice.textContent = deliveryCost ? `${deliveryCost} دج` : 'Price not available';
              totalAmount.textContent = `${totalCost} دج`;
              livraisonPriceInput.value = deliveryCost ? deliveryCost : '';

              livraisonHiddenInput.value = deliveryCost ? deliveryCost : '';
              quantityHiddenInput.value = quantity;
              totalHiddenInput.value = totalCost;
              typeHiddenInput.value = deliveryType;

              totalHiddenInput.value = totalCost;

              console.log('Total amount updated to:', totalCost);
            })
            .catch(error => {
              console.error('Error fetching JSON:', error);
              alert('An error occurred while fetching the delivery price. Please try again.');
            });
        }
      }

      submitButton.addEventListener('click', function(event) {
        const deliveryType = deliveryTypeSelect.value;
        if (deliveryType === 'domicile' && livraisonPriceInput.value === '') {
          event.preventDefault();
          alert('Please select a valid delivery option.');
        }
      });
    });
  </script>


</body>


<script>
  let communeData = [];

  const filePath = 'communeData.json';

  fetch(filePath)
    .then(response => {
      if (!response.ok) {
        throw new Error('Network response was not ok');
      }
      return response.json();
    })
    .then(data => {
      communeData = data;
      console.log('Data loaded successfully:', communeData);
      document.getElementById('wilayas').addEventListener('change', updateCommunes);
    })
    .catch(error => {
      console.error('Error loading JSON:', error);
    });

  function updateCommunes() {
    const wilayaSelect = document.getElementById('wilayas');
    const communeSelect = document.getElementById('commune');
    const selectedWilaya = wilayaSelect.value;

    communeSelect.innerHTML = '<option value="" hidden>البلدية</option>';

    const filteredCommunes = communeData.filter(commune => commune.wilaya_id === selectedWilaya);

    if (filteredCommunes.length > 0) {
      communeSelect.setAttribute('required', 'required');
      communeSelect.classList.remove('disabled');
      communeSelect.removeAttribute('disabled');
    } else {
      communeSelect.removeAttribute('required');
      communeSelect.classList.add('disabled');
      communeSelect.setAttribute('disabled', 'disabled');
    }

    filteredCommunes.forEach(commune => {
      const option = document.createElement('option');
      option.value = commune.ar_name;
      option.textContent = commune.ar_name;
      communeSelect.appendChild(option);
    });
  }
</script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('deliveryForm').addEventListener('submit', function(event) {
      event.preventDefault(); // Prevent default form submission

      // Disable the submit button to prevent double submission
      var validerBtn = document.getElementById('valider');
      var validerText = document.getElementById('validerText');
      var validerLoading = document.getElementById('validerLoading');
      validerBtn.disabled = true;
      validerBtn.style.opacity = '0.2';
      validerText.style.display = 'none';
      validerLoading.style.display = 'inline-block';

      // Get the form data
      var form = this;
      var formData = new FormData(form);
      var data = {};
      formData.forEach((value, key) => {
        data[key] = value;
      });

      // Submit to Google Apps Script using JSONP to avoid CORS
      var script = document.createElement('script');
      var callback = 'callback_' + Math.floor(Math.random() * 1000000);
      window[callback] = function(response) {
        // Remove the script tag
        document.body.removeChild(script);
        delete window[callback];
        
        // Handle the response
        if (response.result === 'success') {
          window.location.href = 'Congratulation.php';
        } else {
          // Re-enable the submit button if there was an error
          validerBtn.disabled = false;
          validerBtn.style.opacity = '1';
          validerText.style.display = 'inline-block';
          validerLoading.style.display = 'none';
          alert('حدث خطأ. يرجى المحاولة مرة أخرى.');
        }
      };

      // Add callback parameter to URL
      var url = form.action + '?callback=' + callback;
      for (var key in data) {
        url += '&' + encodeURIComponent(key) + '=' + encodeURIComponent(data[key]);
      }

      // Add the script to the page
      script.src = url;
      document.body.appendChild(script);
    });
  });
</script>

<script>
  function validateInput() {
    var inputText = document.getElementById("phoneNumber").value;
    var regex = /^0[5-7][0-9]{8}$/;
    if (!regex.test(inputText)) {
      document.getElementById("phoneNumber").value = "";
    }
  }
</script>
<script>
  document.addEventListener("DOMContentLoaded", function() {
    const reviews = document.querySelectorAll(".review");
    let currentReview = 0;

    function showReview(index) {
      reviews.forEach((review, i) => {
        review.classList.toggle("active", i === index);
      });
    }

    document.getElementById("prevBtn").addEventListener("click", function() {
      currentReview =
        currentReview > 0 ? currentReview - 1 : reviews.length - 1;
      showReview(currentReview);
    });

    document.getElementById("nextBtn").addEventListener("click", function() {
      currentReview =
        currentReview < reviews.length - 1 ? currentReview + 1 : 0;
      showReview(currentReview);
    });

    // Initialize the first review
    showReview(currentReview);
  });
</script>
<script src="page.js"></script>

</html>