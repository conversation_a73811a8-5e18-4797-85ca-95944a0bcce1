<?php 
session_start();
session_destroy();
?>
<!DOCTYPE html>
<html lang="ar">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="icon.gif" type="image/x-icon">
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Cairo:wght@200..1000&display=swap"
      rel="stylesheet"
    />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Lato:ital,wght@0,100;0,300;0,400;0,700;0,900;1,100;1,300;1,400;1,700;1,900&display=swap"
      rel="stylesheet"
    />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="hide.css" />
    <link rel="stylesheet" href="all.min.css" />
    <title>Acceuil</title>
  </head>
  <body>
  <?php
  include("Connecte.php");
  $sqlpixel = "SELECT script FROM facebook_pixel ";
  $resultpixel = $conn->query($sqlpixel);

  if ($resultpixel->num_rows > 0) {
    $rowpixel = $resultpixel->fetch_assoc();
    $pixel_id = $rowpixel['script'];
  }
  ?>
  <script>
    ! function(f, b, e, v, n, t, s) {
      if (f.fbq) return;
      n = f.fbq = function() {
        n.callMethod ?
          n.callMethod.apply(n, arguments) : n.queue.push(arguments)
      };
      if (!f._fbq) f._fbq = n;
      n.push = n;
      n.loaded = !0;
      n.version = '2.0';
      n.queue = [];
      t = b.createElement(e);
      t.async = !0;
      t.src = v;
      s = b.getElementsByTagName(e)[0];
      s.parentNode.insertBefore(t, s)
    }(window, document, 'script',
      'https://connect.facebook.net/en_US/fbevents.js');
    fbq('init', '<?php echo $pixel_id; ?>');
    fbq('track', 'PageView');
  </script>
  <noscript>
    <img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=<?php echo $pixel_id; ?>&ev=PageView&noscript=1" />
  </noscript>
    <aside class="sidebar" role="navigation" aria-label="Menu de navigation" aria-hidden="true">
      <button id="exit" aria-label="Fermer le menu">✖</button>
      <nav class="links">
        <ul role="list">
          <li><a href="index.php" aria-current="page">Accueil</a></li>
          <li><a href="store.php">Boutique</a></li>
          <li><a href="livraison.php">Livraison</a></li>
          <li><a href="paiment.php">Paiement</a></li>
          <li><a href="Contact.php">Contact</a></li>
          <li><a href="about.php">À Propos</a></li>
        </ul>
      </nav>
    </aside>
    <div class="Bar">livraison disponible</div>
    <nav class="navbar" role="navigation" aria-label="Navigation principale">
      <button class="right" aria-label="Ouvrir le menu de navigation" aria-expanded="false">
        <i class="fa-solid fa-bars" aria-hidden="true"></i>
      </button>
      <a href="index.php" aria-label="Retour à l'accueil">
        <div class="meduim" role="img" aria-label="Logo Mélisse Education"></div>
      </a>
      <button class="left" aria-label="Rechercher">
        <i class="fa-solid fa-magnifying-glass" aria-hidden="true"></i>
      </button>
    </nav>
    <div class="intro"></div>
    <div class="container">
      <div class="title animate-fade-in-up">
        <div class="first">
          <span style="background: linear-gradient(135deg, #6366f1, #ec4899); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">" Production Algérienne "</span>
        </div>
        <div class="last">
          Création de livres et jeux éducatifs pour enfants
        </div>
        <div class="last" style="font-weight: 600; color: var(--primary-color);">Production Algérienne</div>
      </div>
    </div>
    <div class="container">
      <div class="title">
        <div class="first">Nos Produit</div>
      </div>
      <div class="products">
      <?php
      include("Connecte.php");
      $query = "SELECT * FROM products ";
      $result = mysqli_query($conn, $query);

      while ($row = mysqli_fetch_assoc($result)) {
        $productName = htmlspecialchars($row['ProductName']);
        $productPrice = htmlspecialchars($row['Price']);
        $productPicture = $row['Picture'];
        $Category = $row['Picture'];
        $productName = htmlspecialchars($productName);
        $productPrice = htmlspecialchars($productPrice);

        $ctgQuery = "SELECT CategoryName FROM categories WHERE CategoryID = " . $row['CategoryID'];
        $ctgResult = mysqli_query($conn, $ctgQuery);
        $category = mysqli_fetch_assoc($ctgResult);
        $Ctg=$category['CategoryName'];
      ?>
        <div class="product hover-lift">
          <div class="Picture" style="background-image: url('data:image/jpeg;base64,<?php echo base64_encode($productPicture); ?>');"></div>
          <div class="product-badge">Nouveau</div>
          <div class="description">
            <div class="product-category"><?php echo $Ctg; ?></div>
            <h5 class="product-title"><?php echo $productName; ?></h5>
            <div class="product-price"><?php echo $productPrice; ?> DA</div>
            <a href="Product.php?id=<?php echo htmlspecialchars($row['ProductID']); ?>" class="hover-lift">
              <i class="fa-solid fa-shopping-cart" style="margin-right: 8px;"></i>
              Acheter
            </a>
          </div>
        </div>
      <?php
      }
      ?>
      </div>
    </div>
    <br />
    <br />
    <div class="about_us scroll-animate">
      <h1>
        <i class="fa-solid fa-heart" style="color: var(--secondary-color); margin-right: 12px;"></i>
        Qui Sommes-nous ?
      </h1>
      <p>
        Mélisse Education est une petite famille qui se compose d'une maman qui crée et un papa qui réalise, qui travaillent dur pour le bonheur de vos enfants en leur créant des livres uniques, éducatifs et interactifs avec lesquels ils auront droit à un apprentissage de qualité en s'amusant !
      </p>
      <h3>
        <i class="fa-solid fa-star" style="color: var(--warning-color); margin-right: 8px;"></i>
        Pourquoi Mélisse Éducation !
      </h3>
      <p>
        Les livres éducatifs sont des outils précieux pour accompagner le développement des enfants dès leur plus jeune âge. Découvrez nos livres qui sont interactifs et ludiques, spécialement conçus pour stimuler et développer leur intelligence, leur curiosité et leur langage, leur motricité fine et leur capacité de concentration.
      </p>
      <p>
        Des activités ludiques et interactives, des jeux d'apprentissage seront au rendez-vous pour apprendre tout en s'amusant !
      </p>
    </div>
    <a href="index.php"><div class="meduim"></div></a>
    <div class="footer">
      <div class="foot_links">
        <ul>
          <li>
            <a href="about.php" style="color: #ec165c">à Propos</a>
          </li>
          <li><a href="Contact.php" style="color: #4a92cf">Contact</a></li>
          <li><a href="paiment.php" style="color: green">Payement</a></li>
          <li><a href="livraison.php" style="color: black">livraison</a></li>
        </ul>
      </div>

      <div class="icons" >
        <a href="https://www.facebook.com/fireflygraphiste?mibextid=ZbWKwL" style="color: blue"><i class="fa-brands fa-facebook"></i></a>
        <a href="https://www.instagram.com/melisse_education?igsh=MXdudWgyNW9ocHB4eg==" style="color: orangered"
          ><i class="fa-brands fa-instagram"></i
        ></a>
        <a href="https://www.tiktok.com/@melisse.education?_t=8niKJtPxRX3&_r=1" style="color: Black"
          > <i class="fa-brands fa-tiktok"></i> </a>
      </div>
    </div>
  </body>
  <script src="page.js"></script>
</html>
