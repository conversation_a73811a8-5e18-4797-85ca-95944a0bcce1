<?php

session_start();
if (isset($_SESSION['confirmation_completed']) && $_SESSION['confirmation_completed'] === true) {
    header("Location: store.php");
    exit;
} else {

    if (isset($_GET['id'])) {

        $id = $_GET['id'];
        $OrderID = $_GET['OrderID'];
        $name = $_GET['name'];
        $phone = $_GET['phone'];
        $wilayas = $_GET['wilayas'];
        $commune = $_GET['commune'];
        $adresse = $_GET['adresse'];
        $price = $_GET['price'];
        $Qnt = $_GET['Quantite'];

        include('Connecte.php');



        $query = "SELECT * FROM products WHERE ProductID=$id";
        $result = mysqli_query($conn, $query);
        if ($row = mysqli_fetch_assoc($result)) {
            $n = $row["ProductName"];
            $p = $row["Price"];
            $Picture = $row['Picture'];
        }


        $queryx = "SELECT * FROM product_pictures WHERE ProductID=$id";
        $resultx = mysqli_query($conn, $queryx);

        $Pictures = array();

        if (mysqli_num_rows($resultx) > 0) {
            while ($rowx = mysqli_fetch_assoc($resultx)) {
                $Pictures[] = $rowx['Picture'];
            }
        }


        if (isset($_POST['Delevry'])) {
            $Delevry = $_POST['Delevry'];
        } else {
            $Delevry = "Not specified";
        }


        $wilayaNames = array(
            1 => "أدرار",
            2 => "الشلف",
            3 => "الأغواط",
            4 => "أم البواقي",
            5 => "باتنة",
            6 => "بجاية",
            7 => "بسكرة",
            8 => "بشار",
            9 => "البليدة",
            10 => "البويرة",
            11 => "تمنراست",
            12 => "تبسة",
            13 => "تلمسان",
            14 => "تيارت",
            15 => "تيزي وزو",
            16 => "الجزائر",
            17 => "الجلفة",
            18 => "جيجل",
            19 => "سطيف",
            20 => "سعيدة",
            21 => "سكيكدة",
            22 => "سيدي بلعباس",
            23 => "عنابة",
            24 => "قالمة",
            25 => "قسنطينة",
            26 => "المدية",
            27 => "مستغانم",
            28 => "المسيلة",
            29 => "معسكر",
            30 => "ورقلة",
            31 => "وهران",
            32 => "البيض",
            33 => "إليزي",
            34 => "برج بوعريريج",
            35 => "بومرداس",
            36 => "الطارف",
            37 => "تندوف",
            38 => "تيسمسيلت",
            39 => "الوادي",
            40 => "خنشلة",
            41 => "سوق أهراس",
            42 => "تيبازة",
            43 => "ميلة",
            44 => "عين الدفلى",
            45 => "النعامة",
            46 => "عين تيموشنت",
            47 => "غرداية",
            48 => "غليزان",
            49 => "تيميمون",
            51 => "أولاد جلال",
            52 => "بني عباس",
            53 => "عين صالح",
            55 => "تقرت",
            56 => "جانت",
            57 => "المغير",
            58 => "المنيعة"
        );



        function getWilayaName($wilayas, $wilayaNames)
        {
            return isset($wilayaNames[$wilayas]) ? $wilayaNames[$wilayas] : "Unknown Wilaya";
        }

        $wilayaName = getWilayaName($wilayas, $wilayaNames);
    }

    $LivraisonPrice = $_POST['LivraisonPrice'] ?? 0;
    $total = $p * $Qnt;

    $date = date('Y-m-d');
}
?>




<!DOCTYPE html>
<html lang="ar">

<head>
    <meta charset="UTF-8" />
    <link rel="icon" href="icon.gif" type="image/x-icon">

    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200..1000&display=swap" rel="stylesheet" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="hide.css" />
    <link rel="stylesheet" href="all.min.css" />
    <title>Confirmation</title>
</head>

<script>
    const wilaya = "<?php echo $wilayas; ?>";
</script>

<body>
<?php
  include("Connecte.php");
  $sqlpixel = "SELECT script FROM facebook_pixel ";
  $resultpixel = $conn->query($sqlpixel);

  if ($resultpixel->num_rows > 0) {
    $rowpixel = $resultpixel->fetch_assoc();
    $pixel_id = $rowpixel['script'];
  }
  ?>
  <script>
    ! function(f, b, e, v, n, t, s) {
      if (f.fbq) return;
      n = f.fbq = function() {
        n.callMethod ?
          n.callMethod.apply(n, arguments) : n.queue.push(arguments)
      };
      if (!f._fbq) f._fbq = n;
      n.push = n;
      n.loaded = !0;
      n.version = '2.0';
      n.queue = [];
      t = b.createElement(e);
      t.async = !0;
      t.src = v;
      s = b.getElementsByTagName(e)[0];
      s.parentNode.insertBefore(t, s)
    }(window, document, 'script',
      'https://connect.facebook.net/en_US/fbevents.js');
    fbq('init', '<?php echo $pixel_id; ?>');
    fbq('track', 'PageView');
  </script>
  <noscript>
    <img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=<?php echo $pixel_id; ?>&ev=PageView&noscript=1" />
  </noscript>
    <div class="sidebar">
        <div class="" id="exit">✖</div>
        <div class="links">
            <ul>
                <a href="index.php">
                    <li>Acceuil</li>
                </a>
                <a href="store.php">
                    <li>Boutique</li>
                </a>
                <a href="livraison.php">
                    <li>livraison</li>
                </a>
                <a href="paiment.php">
                    <li>Payement</li>
                </a>
                <a href="Contact.php">
                    <li>Contact</li>
                </a>
                <a href="about.php">
                    <li>à Propos</li>
                </a>

            </ul>
        </div>
    </div>
    <div class="Bar">livraison disponible</div>
    <div class="navbar">
        <div class="right"><i class="fa-solid fa-bars"></i></div>
        <a href="index.php">
            <div class="meduim"></div>
        </a>
        <div class="left"><i class="fa-solid fa-magnifying-glass"></i></div>
    </div>

    <div class="container">

        <div class="pic" style="background-image: url('data:image/jpeg;base64,<?php echo base64_encode($Picture); ?>');"></div>
        <div class="pic_product">
        </div>



        <div class="pic_product">
            <h3>la Résumer de votre commende</h3>
            <br>
            <div class="details">
                <div class="">
                    <p>* Client : </p>
                    <p><?php echo $name ?></p>
                </div>
                <div class="">
                    <p>* Numero : </p>
                    <p><?php echo $phone ?></p>
                </div>
                <div class="">
                    <p>* Wilaya : </p>
                    <p><?php echo $wilayaName ?></p>
                </div>
                <?php if($commune): ?>
                <div class="">
                    <p>* Commune : </p>
                    <p><?php echo $commune ?></p>
                </div>
                <?php endif; ?>
                <div class="">
                    <p>* Quantite :</p>
                    <p><?php echo $Qnt ?></p>
                </div>
                <div class="">
                    <p>* Prix Unitaire :</p>
                    <p><?php echo $price ?> DA</p>
                </div>
            </div>
        </div>
        <div class="details">
            <div class="" style="flex-direction: column;">
                <p>* Adresse :</p><br>
                <p><?php echo $adresse ?></p>
            </div>
        </div>
        <form id="myForm" action="https://script.google.com/macros/s/AKfycbz_QEfC_mihqAehfanJhylAVYP82dCfysQQAIaFV7TD59ehz3-kdF3ZynoXaNZHWBl_Gg/exec" method="post" onsubmit="handleSubmit(event)">


            <div class="Delevry">
                <input type="radio" name="Delevry" id="Desk" value="Desk" required>
                <label for="Desk">Desk</label>

                <input type="radio" name="Delevry" id="Domicile" value="Domicile" required>
                <label for="Domicile">Domicile</label>

            </div>

            <div class="details">
                <div class="" style="display: none;">
                    <p>* Product Price: </p>
                    <p id="productPrice"><?php echo $price ?> DA</p> <!-- Replace with your product price logic -->
                    <input type="hidden" name="ProductPrice" id="productPriceInput" value="<?php echo $price ?>">
                </div>
                <div class="">
                    <p>* Delivery Price: </p>
                    <p id="livraisonPrice">...</p>
                    <input type="hidden" name="LivraisonPrice" id="livraisonPriceInput" value="">
                </div>
                <div class="">
                    <p>* Total Amount: </p>
                    <p><mark id="totalAmount"><?php echo $total ?> DA</mark></p>
                </div>
            </div>


    </div>


    <input type="hidden" name="Code" id="Code" value="<?php echo ($OrderID); ?>" />
    <input type="hidden" name="Date" id="Date" value="<?php echo ($date); ?>" />
    <input type="hidden" name="name" id="name" value="<?php echo ($name); ?>" />
    <input type="hidden" name="number" id="number" value="<?php echo ('0' + $phone); ?>" />
    <input type="hidden" name="Commune" id="Commune" value="<?php echo ($commune); ?>" />
    <input type="hidden" name="adresse" id="adresse" value="<?php echo ($adresse); ?>" />
    <input type="hidden" name="product" id="product" value="<?php echo ($n); ?>" />
    <input type="hidden" name="Price" id="Price" value="<?php echo ($total); ?>" />
    <input type="hidden" name="wilaya" id="wilaya" value="<?php echo ($wilayaName); ?>" />
    <input type="hidden" name="Livraison" id="Livraison" value="" />
    <input type="hidden" name="Quantite" id="Quantite" value="<?php echo ($Qnt); ?>" />
    <input type="hidden" name="Total" id="Total" value="" />
    <input type="hidden" name="Type" id="Type" value="" />







    <div class="valider">
        <button type="submit" id="valider" name="valider">Valider</button>
    </div>
    </form>
    </a>
</body>



<script>
    document.addEventListener('DOMContentLoaded', function() {
        const productPriceInput = document.getElementById('productPriceInput');
        const livraisonPriceInput = document.getElementById('livraisonPriceInput');
        const totalAmountElement = document.getElementById('totalAmount');


        // Initial values
        let price = parseFloat(productPriceInput.value) || 0;
        let Qnt = <?php echo $Qnt; ?>;
        let total = price * Qnt;

        // Function to update the total amount
        function updateTotalAmount() {
            const livraisonPrice = parseFloat(livraisonPriceInput.value) || 0;
            const newTotal = price * Qnt + livraisonPrice;
            totalAmountElement.textContent = newTotal + ' DA';
            document.getElementById('Livraison').value = livraisonPrice;
            document.getElementById('Total').value = newTotal;
        }

        document.getElementById('Desk').addEventListener('change', function() {
            if (this.checked) {
                document.getElementById('Type').value = 'Desk';
                fetchData('Desk.json'); 
            }
        });

        document.getElementById('Domicile').addEventListener('change', function() {
            if (this.checked) {
                document.getElementById('Type').value = 'Domicile';
                fetchData('Domicile.json'); 
            }
        });

        function fetchData(jsonFile) {
            fetch(`${jsonFile}?${new Date().getTime()}`)
                .then(response => response.json())
                .then(data => {
                    const deliveryPrice = getPrice(data, wilaya);
                    const displayPrice = deliveryPrice ? `${deliveryPrice} DA` : 'Price not found';
                    document.getElementById('livraisonPrice').innerText = displayPrice;
                    livraisonPriceInput.value = deliveryPrice;
                    updateTotalAmount();
                })
                .catch(error => console.error('Error fetching data:', error));
        }


        function getPrice(data, wilaya) {
    wilaya = wilaya.toLowerCase();
    for (const price in data) {
        if (data.hasOwnProperty(price)) {
            const wilayas = data[price].map(w => w.toLowerCase());
            if (wilayas.includes(wilaya)) {
                return price;
            }
        }
    }
    return null;
}


        updateTotalAmount();

        fetchData(jsonFile);

    });
</script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        document.getElementById('myForm').addEventListener('submit', function(event) {
            event.preventDefault(); // Prevent default form submission

            var form = this; // Use `this` to refer to the form element
            var formData = new FormData(form);

            var xhr = new XMLHttpRequest();
            xhr.open('POST', form.action, true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            xhr.onreadystatechange = function() {
                if (xhr.readyState === XMLHttpRequest.DONE && xhr.status === 200) {
                    window.location.href = 'Congratulation.php'; // Redirect to Congratulation.php on success
                }
            };
            xhr.send(new URLSearchParams(formData)); // Send form data as URL-encoded
            document.getElementById('valider').disabled = true;
            document.getElementById('valider').style.opacity = '0.2';
        });
    });
</script>

<script src="page.js"></script>

</html>