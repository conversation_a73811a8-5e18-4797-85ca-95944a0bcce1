<!DOCTYPE html>
<html lang="ar">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="icon.gif" type="image/x-icon">

    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Cairo:wght@200..1000&display=swap"
      rel="stylesheet"
    />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="hide.css" />
    <link rel="stylesheet" href="all.min.css" />
    <title>à Propos</title>
  </head>
  <body>
  <?php
  include("Connecte.php");
  $sqlpixel = "SELECT script FROM facebook_pixel ";
  $resultpixel = $conn->query($sqlpixel);

  if ($resultpixel->num_rows > 0) {
    $rowpixel = $resultpixel->fetch_assoc();
    $pixel_id = $rowpixel['script'];
  }
  ?>
  <script>
    ! function(f, b, e, v, n, t, s) {
      if (f.fbq) return;
      n = f.fbq = function() {
        n.callMethod ?
          n.callMethod.apply(n, arguments) : n.queue.push(arguments)
      };
      if (!f._fbq) f._fbq = n;
      n.push = n;
      n.loaded = !0;
      n.version = '2.0';
      n.queue = [];
      t = b.createElement(e);
      t.async = !0;
      t.src = v;
      s = b.getElementsByTagName(e)[0];
      s.parentNode.insertBefore(t, s)
    }(window, document, 'script',
      'https://connect.facebook.net/en_US/fbevents.js');
    fbq('init', '<?php echo $pixel_id; ?>');
    fbq('track', 'PageView');
  </script>
  <noscript>
    <img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=<?php echo $pixel_id; ?>&ev=PageView&noscript=1" />
  </noscript>
    <div class="sidebar">
      <div class="" id="exit">✖</div>
      <div class="links">
        <ul>
          <a href="index.php"> <li>Acceuil</li> </a>
          <a href="store.php"> <li>Boutique</li> </a>
          <a href="livraison.php"> <li>livraison</li> </a>
          <a href="paiment.php"> <li>Payement</li> </a>
          <a href="Contact.php"> <li>Contact</li> </a>
          <a href="about.php"> <li>à Propos</li> </a>
        </ul>
      </div>
    </div>
    <div class="Bar">à Propos</div>
    <div class="navbar">
      <div class="right"><i class="fa-solid fa-bars"></i></div>
      <a href="index.php"><div class="meduim"></div></a>
      <div class="left"><i class="fa-solid fa-magnifying-glass"></i></div>
    </div>

    <div class="inf">à Propos</div>
    <div class="photo" id="about"></div>
    <div class="about_us">
      <h1>Qui Sommes nous ??</h1>
      <br />
      <p>
     Mélisse Education est une petite famille qui se compose d'une maman qui crée et un papa qui réalise, qui travaillent dur pour le bonheur de vos enfants en leur créons des livres uniques, éducatifs et interactifs avec lequels ils auront droit à un apprentissage de qualité en s'amusant !
      </p>
      <br />
      <h3>Pourquoi Mélisse Éducation !</h3>
      <br />
      <p>
      Les livres éducatifs sont des outils précieux pour accompagner le développement des enfants des leur plus jeune âge, découvrez nos livres qui sont interactifs et ludiques spécialement conçue pour stimuler et développer leur intelligence, leur curiosité et leur langage, leur motricité fine et leur capacité de concentration
Des activités ludiques et interactifs, des jeux d'apprentissage seront au rendez-vous pour apprendre tout en s'amusant !
      </p>
    </div>
    <div class="footer" style="font-size: 13px; padding: 10px;">
      <p>Shop 2024</p>
      <p>Copyright</p>
    </div>
  </body>
  <script src="page.js"></script>
</html>
