/* ===== CSS VARIABLES FOR MODERN DESIGN ===== */
:root {
  /* Primary Colors - Educational Theme */
  --primary-color: #6366f1;
  --primary-light: #818cf8;
  --primary-dark: #4f46e5;
  --secondary-color: #ec4899;
  --secondary-light: #f472b6;
  --secondary-dark: #db2777;

  /* Neutral Colors */
  --white: #ffffff;
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* Success & Status Colors */
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --info-color: #3b82f6;

  /* Typography */
  --font-primary: "Cairo", "Lato", sans-serif;
  --font-secondary: "Lato", sans-serif;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

/* ===== RESET & BASE STYLES ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-primary);
  line-height: 1.6;
  color: var(--gray-800);
  background-color: var(--gray-50);
}

/* ===== LINK STYLES ===== */
a {
  color: inherit;
  text-decoration: none;
  transition: var(--transition-fast);
}

a:hover {
  color: var(--primary-color);
}

a:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}
/* ===== HEADER & NAVIGATION ===== */
.Bar {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  padding: var(--spacing-sm) var(--spacing-md);
  width: 100%;
  text-align: center;
  font-size: 0.875rem;
  color: var(--white);
  text-transform: uppercase;
  font-weight: 500;
  letter-spacing: 0.5px;
  box-shadow: var(--shadow-sm);
}

.navbar {
  background: var(--white);
  padding: var(--spacing-lg) var(--spacing-xl);
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: var(--shadow-md);
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(10px);
}

.left i,
.right i {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--gray-100);
  border-radius: 50%;
  cursor: pointer;
  color: var(--gray-700);
  font-size: 1.125rem;
  transition: var(--transition-normal);
  border: 2px solid transparent;
}

.left i:hover,
.right i:hover {
  background: var(--primary-color);
  color: var(--white);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.left i:focus,
.right i:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.meduim {
  background-image: url(logo.jpg);
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  width: 120px;
  height: 60px;
  transition: var(--transition-normal);
  border-radius: var(--radius-md);
}

.meduim:hover {
  transform: scale(1.05);
}

/* ===== HERO SECTION ===== */
.intro {
  background-image: linear-gradient(rgba(99, 102, 241, 0.1), rgba(99, 102, 241, 0.2)), url(home.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  background-attachment: fixed;
  width: 100%;
  height: 70vh;
  min-height: 500px;
  max-height: 800px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.intro::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.8), rgba(236, 72, 153, 0.6));
  z-index: 1;
}

.intro > * {
  position: relative;
  z-index: 2;
}

/* ===== CONTAINER & LAYOUT ===== */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-xl) var(--spacing-lg);
}

.title {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
}

.first {
  font-size: 2.5rem;
  font-weight: 700;
  position: relative;
  width: fit-content;
  margin: 0 auto var(--spacing-lg);
  color: var(--gray-900);
  letter-spacing: -0.025em;
}

.first::after {
  content: "";
  position: absolute;
  background: linear-gradient(90deg, var(--secondary-color), var(--primary-color));
  width: 100%;
  height: 4px;
  bottom: -8px;
  left: 0;
  border-radius: var(--radius-sm);
}

.last {
  font-weight: 400;
  font-size: 1.125rem;
  color: var(--gray-600);
  margin: var(--spacing-md) auto;
  max-width: 600px;
  line-height: 1.7;
}

/* ===== PRODUCT GRID ===== */
.products {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-xl);
  margin-top: var(--spacing-3xl);
  padding: 0 var(--spacing-md);
}

.products > div {
  background: var(--white);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: var(--transition-normal);
  border: 1px solid var(--gray-200);
  height: auto;
  min-height: 420px;
}

.products > div:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
  border-color: var(--primary-light);
}

.Picture {
  height: 240px;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  position: relative;
  overflow: hidden;
}

.Picture::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(99, 102, 241, 0.1), rgba(236, 72, 153, 0.1));
  opacity: 0;
  transition: var(--transition-normal);
}

.products > div:hover .Picture::before {
  opacity: 1;
}

/* ===== PRODUCT ENHANCEMENTS ===== */
.product {
  position: relative;
  overflow: hidden;
}

.product::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(45deg);
  transition: var(--transition-slow);
  opacity: 0;
  z-index: 1;
}

.product:hover::before {
  animation: shimmer 1.5s ease-in-out;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
    opacity: 0;
  }
}

.product-badge {
  position: absolute;
  top: var(--spacing-md);
  right: var(--spacing-md);
  background: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark));
  color: var(--white);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  z-index: 2;
  box-shadow: var(--shadow-md);
}

.product-category {
  display: inline-block;
  background: var(--gray-100);
  color: var(--gray-600);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--spacing-sm);
}

.product-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--gray-900);
  line-height: 1.4;
  margin-bottom: var(--spacing-sm);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-price {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: var(--spacing-lg);
}

.product-price::before {
  content: '';
  width: 20px;
  height: 2px;
  background: var(--primary-color);
  border-radius: 1px;
}

.product-price::after {
  content: '';
  width: 20px;
  height: 2px;
  background: var(--primary-color);
  border-radius: 1px;
}

/* ===== PRODUCT DESCRIPTION ===== */
.description {
  padding: var(--spacing-xl);
  text-align: center;
  height: auto;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.description h6 {
  color: var(--gray-500);
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--spacing-xs);
}

.description h5 {
  color: var(--gray-900);
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.4;
  margin-bottom: var(--spacing-sm);
}

#price {
  color: var(--primary-color);
  font-weight: 700;
  font-size: 1.5rem;
  margin-bottom: var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
}

/* ===== BUTTONS ===== */
.description a,
.Categoryes div a {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-md) var(--spacing-xl);
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: var(--white);
  font-weight: 600;
  text-decoration: none;
  font-size: 0.875rem;
  border-radius: var(--radius-lg);
  transition: var(--transition-normal);
  border: none;
  cursor: pointer;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: var(--shadow-md);
  margin-top: auto;
}

.description a:hover,
.Categoryes div a:hover {
  background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  color: var(--white);
}

.description a:active,
.Categoryes div a:active {
  transform: translateY(0);
}


/* ===== SIDEBAR NAVIGATION ===== */
.sidebar {
  position: fixed;
  width: 320px;
  height: 100vh;
  margin: 0;
  padding: 0;
  background: var(--white);
  z-index: 1000;
  left: -320px;
  top: 0;
  transition: var(--transition-normal);
  box-shadow: var(--shadow-xl);
  backdrop-filter: blur(10px);
  border-right: 1px solid var(--gray-200);
}

.sidebar.active {
  left: 0;
}

#exit {
  position: absolute;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--gray-100);
  color: var(--gray-700);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-fast);
  border: 2px solid transparent;
}

#exit:hover {
  background: var(--error-color);
  color: var(--white);
  transform: rotate(90deg);
}

.links {
  margin-top: 80px;
  padding: var(--spacing-lg);
}

.links ul {
  list-style: none;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.links ul li {
  border-radius: var(--radius-lg);
  transition: var(--transition-fast);
  overflow: hidden;
}

.links ul a {
  display: block;
  padding: var(--spacing-lg);
  color: var(--gray-700);
  font-weight: 500;
  text-decoration: none;
  transition: var(--transition-fast);
  border-left: 4px solid transparent;
}

.links ul a:hover {
  background: var(--primary-color);
  color: var(--white);
  border-left-color: var(--secondary-color);
  transform: translateX(8px);
}

/* ===== FORM STYLES ===== */
.form {
  padding: var(--spacing-xl);
  max-width: 600px;
  margin: 0 auto;
}

form input,
form select,
.searsh select {
  width: 100%;
  margin: var(--spacing-md) 0;
  padding: var(--spacing-lg);
  outline: none;
  border: 2px solid var(--gray-200);
  height: 56px;
  font-size: 1rem;
  border-radius: var(--radius-lg);
  background: var(--white);
  color: var(--gray-800);
  transition: var(--transition-fast);
  font-family: var(--font-primary);
}

form input:focus,
form select:focus,
.searsh select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

form input::placeholder {
  color: var(--gray-400);
}

form select {
  cursor: pointer;
}

.search {
  position: relative;
  max-width: 400px;
  margin: var(--spacing-xl) auto;
}

#rechercher {
  position: absolute;
  left: var(--spacing-xs);
  top: 50%;
  height: 48px;
  width: 48px;
  transform: translateY(-50%);
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: var(--white);
  outline: none;
  border: none;
  font-size: 1.125rem;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

#rechercher:hover {
  background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
  transform: translateY(-50%) scale(1.05);
}

.searsh {
  display: flex;
  align-items: center;
  position: relative;
  height: fit-content;
}
.searsh button {
  left: 0px;
  position: absolute;
  background-color: rgba(76, 0, 255, 0.511);
  height: 70%;
  width: 50px;
  top: 50%;
  transform: translateY(-50%);
  display: grid;
  place-content: center;
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
}

.pic_product {
  text-align: center;
}
.pic_product h2 {
  text-align: center;
  color: rgba(0, 0, 0, 0.822);
}

.pic .picx {
  margin: auto;
  background-size: cover;
  width: 80%;
  height: 700px;
  background-repeat: no-repeat;
  margin-bottom: 20px;
  background-position: center;
}

.pic {
  background-image: url(Product.jpg);
}

/* ===== ENHANCED DETAILS SECTION ===== */
.details {
  margin: var(--spacing-xl) auto;
  max-width: 600px;
  padding: var(--spacing-xl);
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.05), rgba(236, 72, 153, 0.05));
  border-radius: var(--radius-xl);
  border: 2px solid var(--gray-200);
  box-shadow: var(--shadow-lg);
  backdrop-filter: blur(10px);
}

.details div {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md) 0;
  border-bottom: 1px solid var(--gray-200);
  position: relative;
}

.details div:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.details div p:first-child {
  font-weight: 700;
  font-size: 1.125rem;
  color: var(--gray-900);
}

.details div p:last-child {
  font-weight: 500;
  color: var(--gray-600);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.details mark {
  background: linear-gradient(135deg, var(--success-color), #059669);
  color: var(--white);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  font-weight: 700;
  font-size: 1.25rem;
  box-shadow: var(--shadow-md);
  border: none;
}

/* ===== ENHANCED FORM BUTTONS ===== */
.valider {
  margin: var(--spacing-xl) auto;
  width: fit-content;
  text-align: center;
}

.valider button {
  outline: none;
  border: none;
  background: linear-gradient(135deg, var(--success-color), #059669);
  color: var(--white);
  min-width: 200px;
  height: 56px;
  margin: var(--spacing-lg);
  font-size: 1.125rem;
  font-weight: 600;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  cursor: pointer;
  transition: var(--transition-normal);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
}

.valider button:hover {
  background: linear-gradient(135deg, #059669, var(--success-color));
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

.valider button:active {
  transform: translateY(0);
}

.valider button:disabled {
  background: var(--gray-400);
  cursor: not-allowed;
  transform: none;
  box-shadow: var(--shadow-sm);
}

/* ===== FORM VALIDATION STYLES ===== */
.form-group {
  position: relative;
  margin-bottom: var(--spacing-lg);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-sm);
  font-weight: 600;
  color: var(--gray-700);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.form-input {
  width: 100%;
  padding: var(--spacing-lg);
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-lg);
  font-size: 1rem;
  transition: var(--transition-fast);
  background: var(--white);
}

.form-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-input.error {
  border-color: var(--error-color);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-input.success {
  border-color: var(--success-color);
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.form-error {
  color: var(--error-color);
  font-size: 0.875rem;
  margin-top: var(--spacing-xs);
  display: none;
}

.form-error.show {
  display: block;
}

.inf {
  background-color: rgb(31, 33, 50);
  color: white;
  text-align: center;
  font-size: 25px;
  margin: 0;
  padding: 50px;
  font-weight: bold;
}

.photo {
  margin-top: 20px;
  background-size: contain;
  width: 100%;
  height: 500px;
  margin: 0 auto;
  background-position: center;
  background-repeat: no-repeat;
}
#paiment {
  background-image: url(cod.webp);
}
#livraison {
  background-image: url(shipping-delivery-soq-dz-final.webp);
}
#about {
  background-image: url(about.jpg);
}
#Contact {
  background-image: url(Contact.jpg);
}

mark {
  background-color: #4eaf52;
  border-radius: 3px;
  padding: 1px;
  color: white;
}

.information {
  text-align: start;
  padding: 15px;
}
.information h4 {
  margin: 15px auto;
}

.footer {
  background-color: #eee;
  padding: 20px;
  font-size: 20px;
  text-align: center;
}

.icons a {
  margin: 10px;
  font-size: 25px;
}

.Categoryes > div {
  background-color: #eee;
  margin: 30px auto;
  height: 400px;
  width: 90%;
  border-radius: 10px;
  padding: 15px;
  background-position: center;
  background-size: cover;
}
.Categoryes > div p {
  margin: 50px 0px 25px;
  font-weight: bold;
  font-size: 20px;
  color: black;
}

.Buttons {
  position: relative;
  overflow: hidden;
}

/* .assest_5{
  background-image: url(1x/Asset\ 5.png);
  background-size: contain;
  width: 550px;
  height: 100%;
  background-repeat: no-repeat;
  transform: rotate(-0.25turn);
  position: absolute;
  top: 0;
  left: 20px;
} */
.assest_1,
.assest_2,
.assest_3,
.assest_4,
.assest_5 {
  background-size: contain;
  width: 225px;
  height: 150px;
  background-repeat: no-repeat;
  margin: 40px auto;
  display: grid;
  place-content: center;
  font-weight: bolder;
  font-size: 20px;
  text-transform: uppercase;
  color: white;
}

/* ===== ENHANCED DELIVERY OPTIONS ===== */
.Delevry {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  margin: var(--spacing-xl) 0;
  background: var(--white);
  padding: var(--spacing-xl);
  border-radius: var(--radius-xl);
  border: 2px solid var(--gray-200);
  box-shadow: var(--shadow-md);
}

.Delevry input[type="radio"] {
  display: none;
}

.Delevry label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  color: var(--gray-700);
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  transition: var(--transition-fast);
  border: 2px solid var(--gray-200);
  background: var(--gray-50);
  position: relative;
  overflow: hidden;
}

.Delevry label::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
  transition: var(--transition-normal);
}

.Delevry label:hover::before {
  left: 100%;
}

.Delevry label span {
  width: 24px;
  height: 24px;
  display: inline-block;
  margin-right: var(--spacing-md);
  position: relative;
  border: 2px solid var(--gray-300);
  border-radius: 50%;
  background: var(--white);
  transition: var(--transition-fast);
}

.Delevry input[type="radio"]:checked + label {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  color: var(--white);
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.Delevry input[type="radio"]:checked + label span {
  border-color: var(--white);
  background: var(--white);
}

.Delevry input[type="radio"]:checked + label span::before {
  content: "";
  width: 12px;
  height: 12px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--primary-color);
  border-radius: 50%;
}

.Delevry label:hover {
  background: var(--gray-100);
  border-color: var(--primary-light);
  transform: translateY(-1px);
}

a {
  text-decoration: none;
}

.assest_1 {
  background-image: url(1x/Asset\ 4.png);
}
.assest_2 {
  background-image: url(1x/Asset\ 1.png);
}
.assest_3 {
  background-image: url(1x/Asset\ 2.png);
}
.assest_4 {
  background-image: url(1x/Asset\ 3.png);
}
.assest_5 {
  background-image: url(1x/Asset\ 6.png);
  width: 30px;
  height: 70px;
  margin-top: 100px;
}

/* ===== ABOUT US SECTION ===== */
.about_us {
  background: var(--white);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-3xl);
  margin: var(--spacing-3xl) auto;
  max-width: 800px;
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--gray-200);
}

.about_us h1 {
  color: var(--gray-900);
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: var(--spacing-xl);
  text-align: center;
}

.about_us h3 {
  color: var(--primary-color);
  font-size: 1.5rem;
  font-weight: 600;
  margin: var(--spacing-xl) 0 var(--spacing-lg);
}

.about_us p {
  font-weight: 400;
  line-height: 1.8;
  color: var(--gray-700);
  font-size: 1.125rem;
  margin-bottom: var(--spacing-lg);
}

/* ===== FOOTER ===== */
.footer {
  background: linear-gradient(135deg, var(--gray-900), var(--gray-800));
  padding: var(--spacing-3xl) var(--spacing-xl);
  color: var(--white);
  text-align: center;
  margin-top: var(--spacing-3xl);
}

.foot_links ul {
  list-style: none;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
}

.foot_links ul li a {
  color: var(--gray-300);
  font-weight: 500;
  text-decoration: none;
  transition: var(--transition-fast);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
}

.foot_links ul li a:hover {
  color: var(--white);
  background: rgba(255, 255, 255, 0.1);
}

.icons {
  display: flex;
  justify-content: center;
  gap: var(--spacing-lg);
  margin-top: var(--spacing-xl);
}

.icons a {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transition: var(--transition-normal);
  font-size: 1.25rem;
}

.icons a:hover {
  background: var(--primary-color);
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.disabled {
  opacity: 0.4;
  pointer-events: none;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .container {
    padding: var(--spacing-lg) var(--spacing-md);
  }

  .navbar {
    padding: var(--spacing-md) var(--spacing-lg);
  }

  .meduim {
    width: 100px;
    height: 50px;
  }

  .intro {
    height: 60vh;
    min-height: 400px;
    background-attachment: scroll;
  }

  .first {
    font-size: 2rem;
  }

  .last {
    font-size: 1rem;
  }

  .products {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
  }

  .products > div {
    min-height: 380px;
  }

  .Picture {
    height: 200px;
  }

  .description {
    padding: var(--spacing-lg);
  }

  .description h5 {
    font-size: 1.125rem;
  }

  #price {
    font-size: 1.25rem;
  }

  .form {
    padding: var(--spacing-lg);
  }

  .form input,
  .form select {
    height: 48px;
    font-size: 0.875rem;
  }

  .sidebar {
    width: 280px;
    left: -280px;
  }

  .sidebar.active {
    left: 0;
  }

  .about_us {
    padding: var(--spacing-xl);
    margin: var(--spacing-xl) var(--spacing-md);
  }

  .about_us h1 {
    font-size: 1.75rem;
  }

  .about_us h3 {
    font-size: 1.25rem;
  }

  .about_us p {
    font-size: 1rem;
  }

  .foot_links ul {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .icons {
    gap: var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .products {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .first {
    font-size: 1.75rem;
  }

  .navbar {
    padding: var(--spacing-sm) var(--spacing-md);
  }

  .left i,
  .right i {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .meduim {
    width: 80px;
    height: 40px;
  }

  .sidebar {
    width: 100%;
    left: -100%;
  }

  .about_us {
    padding: var(--spacing-lg);
  }

  .form {
    padding: var(--spacing-md);
  }

  /* Mobile-specific touch improvements */
  .left i,
  .right i {
    min-height: 44px;
    min-width: 44px;
  }

  .description a,
  .valider button {
    min-height: 44px;
    padding: var(--spacing-md) var(--spacing-lg);
  }

  .Delevry label {
    min-height: 44px;
    padding: var(--spacing-md);
  }

  /* Improve text readability on mobile */
  .about_us p {
    line-height: 1.8;
  }

  /* Better spacing for mobile forms */
  .details {
    margin: var(--spacing-lg) var(--spacing-sm);
    padding: var(--spacing-lg);
  }

  /* Mobile notification adjustments */
  .notification {
    right: var(--spacing-sm) !important;
    left: var(--spacing-sm) !important;
    max-width: calc(100% - 2rem) !important;
  }
}

/* ===== ANIMATIONS & KEYFRAMES ===== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

/* ===== ANIMATION CLASSES ===== */
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-left {
  animation: fadeInLeft 0.6s ease-out;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

/* ===== SCROLL ANIMATIONS ===== */
.scroll-animate {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease-out;
}

.scroll-animate.visible {
  opacity: 1;
  transform: translateY(0);
}

/* ===== LOADING SPINNER ===== */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* ===== ENHANCED HOVER EFFECTS ===== */
.hover-lift {
  transition: var(--transition-normal);
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

/* ===== FOCUS STYLES FOR ACCESSIBILITY ===== */
*:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

button:focus,
input:focus,
select:focus,
textarea:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* ===== UTILITY CLASSES ===== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }

.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mt-xl { margin-top: var(--spacing-xl); }

.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }

/* ===== ADVANCED VISUAL EFFECTS ===== */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.gradient-text {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.floating {
  animation: floating 3s ease-in-out infinite;
}

@keyframes floating {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.glow {
  box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
}

.glow:hover {
  box-shadow: 0 0 30px rgba(99, 102, 241, 0.5);
}

/* ===== ENHANCED SCROLLBAR ===== */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--primary-dark), var(--secondary-dark));
}

/* ===== SELECTION STYLING ===== */
::selection {
  background: var(--primary-color);
  color: var(--white);
}

::-moz-selection {
  background: var(--primary-color);
  color: var(--white);
}

/* ===== ENHANCED FOCUS INDICATORS ===== */
.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.3);
}

/* ===== LOADING STATES ===== */
.skeleton {
  background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-100) 50%, var(--gray-200) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* ===== ENHANCED BUTTONS ===== */
.btn-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: var(--white);
  border: none;
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--radius-lg);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  cursor: pointer;
  transition: var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: var(--transition-normal);
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

/* ===== ENHANCED CARDS ===== */
.card-enhanced {
  background: var(--white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  transition: var(--transition-normal);
  border: 1px solid var(--gray-200);
  position: relative;
  overflow: hidden;
}

.card-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  transform: scaleX(0);
  transition: var(--transition-normal);
}

.card-enhanced:hover::before {
  transform: scaleX(1);
}

.card-enhanced:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */
/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --primary-color: #0000ff;
    --secondary-color: #ff0000;
    --gray-600: #000000;
    --gray-700: #000000;
    --gray-800: #000000;
    --gray-900: #000000;
  }

  .products > div {
    border: 2px solid #000000;
  }

  .description a {
    border: 2px solid #ffffff;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .floating {
    animation: none;
  }

  .intro {
    background-attachment: scroll;
  }
}

/* Enhanced focus indicators */
.focus-visible {
  outline: 3px solid var(--primary-color);
  outline-offset: 2px;
}

button:focus-visible,
a:focus-visible,
input:focus-visible,
select:focus-visible {
  outline: 3px solid var(--primary-color);
  outline-offset: 2px;
  box-shadow: 0 0 0 6px rgba(99, 102, 241, 0.2);
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Skip link */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary-color);
  color: var(--white);
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 10000;
  font-weight: 600;
}

.skip-link:focus {
  top: 6px;
}

/* Enhanced button states */
button:disabled,
a.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

/* Better color contrast for text */
.text-high-contrast {
  color: var(--gray-900);
  font-weight: 600;
}

/* Keyboard navigation improvements */
.keyboard-nav button:focus,
.keyboard-nav a:focus {
  background: var(--primary-color);
  color: var(--white);
  transform: scale(1.05);
}

/* Error states with better contrast */
.error-text {
  color: #d32f2f;
  font-weight: 600;
}

.success-text {
  color: #2e7d32;
  font-weight: 600;
}

/* Enhanced form labels */
.form-label-enhanced {
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--spacing-sm);
  display: block;
}

/* Required field indicators */
.required::after {
  content: ' *';
  color: var(--error-color);
  font-weight: bold;
}

/* ARIA live regions */
.live-region {
  position: absolute;
  left: -10000px;
  width: 1px;
  height: 1px;
  overflow: hidden;
}


.product_dz{
  margin: 10px;
  padding: 5px;
  padding-bottom: 10px;
}
.product_dz h2{
font-weight: light;
}
.product_dz p{
  margin-top: 10px;
  font-size: 20px;
  font-weight: bold;
  color: #259bea;
}

#slide {
  margin: 0 auto;
  position: relative;
  text-align: center;
}

.pic {
  width: 100%;
  display: none; 
}

#main-pic {
  display: block; 
}

button {
  margin-top: 10px;
  padding: 5px 10px;
}

.slider-btn {
  position: absolute;
  bottom:75px;
  transform: translateY(-50%);
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  padding: 10px 20px;
  cursor: pointer;
  z-index: 100;
}

#prev {
  left: 10px;
}

#next {
  right: 10px;
}


.reviews {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 30px;
}

.review-content {
  max-width: 600px;
  margin: 0 20px;
  overflow: hidden;
  position: relative;
}

.review {
  display: none;
  text-align: center;
}

.review.active {
  display: block;
}

.review img {
  width: 100%;
  border-radius: 8px;
}

.nav-button {
  background-color: #28a745;
  color: #fff;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
}

.nav-button:hover {
  background-color: #218838;
}