// Enhanced sidebar navigation with smooth animations
let bar = document.querySelector(".right i");
let menu = document.querySelector(".sidebar");
let exit = document.querySelector("#exit");

// Create overlay for mobile
const overlay = document.createElement('div');
overlay.style.cssText = `
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
`;
document.body.appendChild(overlay);

function openSidebar() {
  menu.classList.add('active');
  overlay.style.opacity = '1';
  overlay.style.visibility = 'visible';
  document.body.style.overflow = 'hidden';
}

function closeSidebar() {
  menu.classList.remove('active');
  overlay.style.opacity = '0';
  overlay.style.visibility = 'hidden';
  document.body.style.overflow = '';
}

bar.onclick = openSidebar;
exit.onclick = closeSidebar;
overlay.onclick = closeSidebar;

// Close sidebar on escape key
document.addEventListener('keydown', function(e) {
  if (e.key === 'Escape' && menu.classList.contains('active')) {
    closeSidebar();
  }
});

// Add smooth scroll behavior for navigation links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
  anchor.addEventListener('click', function (e) {
    e.preventDefault();
    const target = document.querySelector(this.getAttribute('href'));
    if (target) {
      target.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
  });
});

// Add loading animation for page transitions
function addLoadingAnimation() {
  const links = document.querySelectorAll('a[href$=".php"]');
  links.forEach(link => {
    link.addEventListener('click', function(e) {
      if (!this.href.includes('#')) {
        // Add loading spinner
        const spinner = document.createElement('div');
        spinner.innerHTML = '<i class="fa fa-spinner fa-spin"></i>';
        spinner.style.cssText = `
          position: fixed;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          z-index: 9999;
          color: #6366f1;
          font-size: 2rem;
        `;
        document.body.appendChild(spinner);
      }
    });
  });
}

// Initialize loading animations
addLoadingAnimation();

// Scroll animations
function initScrollAnimations() {
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  };

  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('visible');
      }
    });
  }, observerOptions);

  // Add scroll animation to products
  document.querySelectorAll('.products > div').forEach((product, index) => {
    product.classList.add('scroll-animate');
    product.style.animationDelay = `${index * 0.1}s`;
    observer.observe(product);
  });

  // Add scroll animation to other elements
  document.querySelectorAll('.about_us, .title, .container').forEach(element => {
    element.classList.add('scroll-animate');
    observer.observe(element);
  });
}

// Initialize scroll animations when DOM is loaded
document.addEventListener('DOMContentLoaded', initScrollAnimations);

// Add smooth hover effects to interactive elements
function addHoverEffects() {
  document.querySelectorAll('.products > div').forEach(product => {
    product.classList.add('hover-lift');
  });
}

// Initialize hover effects
addHoverEffects();

// Enhanced form validation
function initFormValidation() {
  const forms = document.querySelectorAll('form');

  forms.forEach(form => {
    const inputs = form.querySelectorAll('input, select');

    inputs.forEach(input => {
      // Add real-time validation
      input.addEventListener('blur', function() {
        validateField(this);
      });

      input.addEventListener('input', function() {
        if (this.classList.contains('error')) {
          validateField(this);
        }
      });
    });

    // Enhanced form submission
    form.addEventListener('submit', function(e) {
      let isValid = true;

      inputs.forEach(input => {
        if (!validateField(input)) {
          isValid = false;
        }
      });

      if (!isValid) {
        e.preventDefault();
        showNotification('Veuillez corriger les erreurs dans le formulaire', 'error');
      } else {
        // Show loading state
        const submitBtn = form.querySelector('button[type="submit"]');
        if (submitBtn) {
          const originalText = submitBtn.innerHTML;
          submitBtn.innerHTML = '<span class="loading-spinner"></span> Traitement...';
          submitBtn.disabled = true;

          // Re-enable after 3 seconds if form hasn't been submitted
          setTimeout(() => {
            if (submitBtn.disabled) {
              submitBtn.innerHTML = originalText;
              submitBtn.disabled = false;
            }
          }, 3000);
        }
      }
    });
  });
}

function validateField(field) {
  const value = field.value.trim();
  const fieldType = field.type;
  const isRequired = field.hasAttribute('required');

  // Remove existing error states
  field.classList.remove('error', 'success');

  let isValid = true;
  let errorMessage = '';

  if (isRequired && !value) {
    isValid = false;
    errorMessage = 'Ce champ est requis';
  } else if (value) {
    switch (fieldType) {
      case 'email':
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
          isValid = false;
          errorMessage = 'Adresse email invalide';
        }
        break;
      case 'tel':
      case 'number':
        if (field.id === 'phoneNumber') {
          const phoneRegex = /^0[5-7][0-9]{8}$/;
          if (!phoneRegex.test(value)) {
            isValid = false;
            errorMessage = 'Numéro de téléphone invalide (format: 0XXXXXXXX)';
          }
        }
        break;
    }
  }

  // Apply validation styles
  if (isValid && value) {
    field.classList.add('success');
  } else if (!isValid) {
    field.classList.add('error');
  }

  return isValid;
}

// Notification system
function showNotification(message, type = 'info') {
  const notification = document.createElement('div');
  notification.className = `notification notification-${type}`;
  notification.innerHTML = `
    <i class="fa-solid fa-${type === 'error' ? 'exclamation-circle' : type === 'success' ? 'check-circle' : 'info-circle'}"></i>
    <span>${message}</span>
    <button onclick="this.parentElement.remove()">
      <i class="fa-solid fa-times"></i>
    </button>
  `;

  // Add notification styles
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: ${type === 'error' ? 'var(--error-color)' : type === 'success' ? 'var(--success-color)' : 'var(--info-color)'};
    color: white;
    padding: 1rem 1.5rem;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    z-index: 10000;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    max-width: 400px;
    animation: slideInRight 0.3s ease-out;
  `;

  document.body.appendChild(notification);

  // Auto remove after 5 seconds
  setTimeout(() => {
    if (notification.parentElement) {
      notification.style.animation = 'slideOutRight 0.3s ease-in';
      setTimeout(() => notification.remove(), 300);
    }
  }, 5000);
}

// Initialize form validation
document.addEventListener('DOMContentLoaded', initFormValidation);

// Mobile-specific enhancements
function initMobileEnhancements() {
  // Add touch feedback for interactive elements
  const touchElements = document.querySelectorAll('button, .product, .description a, .left i, .right i');

  touchElements.forEach(element => {
    element.addEventListener('touchstart', function() {
      this.style.transform = 'scale(0.98)';
    });

    element.addEventListener('touchend', function() {
      this.style.transform = '';
    });
  });

  // Improve scroll performance on mobile
  let ticking = false;

  function updateScrollPosition() {
    // Add scroll-based effects here if needed
    ticking = false;
  }

  window.addEventListener('scroll', function() {
    if (!ticking) {
      requestAnimationFrame(updateScrollPosition);
      ticking = true;
    }
  });

  // Prevent zoom on double tap for form inputs
  const inputs = document.querySelectorAll('input, select, textarea');
  inputs.forEach(input => {
    input.addEventListener('touchend', function(e) {
      e.preventDefault();
      this.focus();
    });
  });

  // Add swipe gesture for sidebar
  let startX = 0;
  let startY = 0;

  document.addEventListener('touchstart', function(e) {
    startX = e.touches[0].clientX;
    startY = e.touches[0].clientY;
  });

  document.addEventListener('touchmove', function(e) {
    if (!startX || !startY) return;

    const diffX = startX - e.touches[0].clientX;
    const diffY = startY - e.touches[0].clientY;

    // Only handle horizontal swipes
    if (Math.abs(diffX) > Math.abs(diffY)) {
      // Swipe right to open sidebar (from left edge)
      if (startX < 50 && diffX < -50 && !menu.classList.contains('active')) {
        openSidebar();
      }
      // Swipe left to close sidebar
      else if (diffX > 50 && menu.classList.contains('active')) {
        closeSidebar();
      }
    }

    startX = 0;
    startY = 0;
  });
}

// Initialize mobile enhancements
document.addEventListener('DOMContentLoaded', initMobileEnhancements);

// Visual enhancements
function initVisualEnhancements() {
  // Add floating animation to logo
  const logo = document.querySelector('.meduim');
  if (logo) {
    logo.classList.add('floating');
  }

  // Add glow effect to important buttons
  const importantButtons = document.querySelectorAll('.valider button, .description a');
  importantButtons.forEach(btn => {
    btn.classList.add('glow');
  });

  // Add enhanced card effects to products
  const products = document.querySelectorAll('.product');
  products.forEach(product => {
    product.classList.add('card-enhanced');
  });

  // Add gradient text effect to titles
  const titles = document.querySelectorAll('.first');
  titles.forEach(title => {
    title.classList.add('gradient-text');
  });

  // Add parallax effect to intro section
  const intro = document.querySelector('.intro');
  if (intro) {
    window.addEventListener('scroll', () => {
      const scrolled = window.pageYOffset;
      const rate = scrolled * -0.5;
      intro.style.transform = `translateY(${rate}px)`;
    });
  }

  // Add smooth reveal animations
  const revealElements = document.querySelectorAll('.about_us, .container, .footer');
  const revealObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.style.animation = 'fadeInUp 0.8s ease-out';
        entry.target.style.opacity = '1';
      }
    });
  }, { threshold: 0.1 });

  revealElements.forEach(element => {
    element.style.opacity = '0';
    revealObserver.observe(element);
  });

  // Add typing effect to marquee text (if present)
  const marquee = document.querySelector('marquee');
  if (marquee) {
    const text = marquee.textContent;
    marquee.innerHTML = '';
    let i = 0;

    function typeWriter() {
      if (i < text.length) {
        marquee.innerHTML += text.charAt(i);
        i++;
        setTimeout(typeWriter, 100);
      }
    }

    // Start typing effect after a delay
    setTimeout(typeWriter, 1000);
  }

  // Add ripple effect to buttons
  function createRipple(event) {
    const button = event.currentTarget;
    const circle = document.createElement('span');
    const diameter = Math.max(button.clientWidth, button.clientHeight);
    const radius = diameter / 2;

    circle.style.width = circle.style.height = `${diameter}px`;
    circle.style.left = `${event.clientX - button.offsetLeft - radius}px`;
    circle.style.top = `${event.clientY - button.offsetTop - radius}px`;
    circle.classList.add('ripple');

    const ripple = button.getElementsByClassName('ripple')[0];
    if (ripple) {
      ripple.remove();
    }

    button.appendChild(circle);
  }

  // Add ripple effect styles
  const style = document.createElement('style');
  style.textContent = `
    .ripple {
      position: absolute;
      border-radius: 50%;
      transform: scale(0);
      animation: ripple 600ms linear;
      background-color: rgba(255, 255, 255, 0.6);
    }

    @keyframes ripple {
      to {
        transform: scale(4);
        opacity: 0;
      }
    }
  `;
  document.head.appendChild(style);

  // Apply ripple effect to buttons
  const buttons = document.querySelectorAll('button, .description a');
  buttons.forEach(button => {
    button.style.position = 'relative';
    button.style.overflow = 'hidden';
    button.addEventListener('click', createRipple);
  });
}

// Initialize visual enhancements
document.addEventListener('DOMContentLoaded', initVisualEnhancements);

// Accessibility enhancements
function initAccessibilityFeatures() {
  // Add skip link
  const skipLink = document.createElement('a');
  skipLink.href = '#main-content';
  skipLink.className = 'skip-link';
  skipLink.textContent = 'Passer au contenu principal';
  document.body.insertBefore(skipLink, document.body.firstChild);

  // Add main content landmark
  const container = document.querySelector('.container');
  if (container) {
    container.id = 'main-content';
    container.setAttribute('role', 'main');
  }

  // Enhance keyboard navigation
  document.addEventListener('keydown', function(e) {
    // ESC key to close modals/sidebar
    if (e.key === 'Escape') {
      if (menu.classList.contains('active')) {
        closeSidebar();
      }
    }

    // Tab navigation improvements
    if (e.key === 'Tab') {
      document.body.classList.add('keyboard-nav');
    }
  });

  // Remove keyboard nav class on mouse use
  document.addEventListener('mousedown', function() {
    document.body.classList.remove('keyboard-nav');
  });

  // Add ARIA labels and roles
  const searchButton = document.querySelector('#rechercher');
  if (searchButton) {
    searchButton.setAttribute('aria-label', 'Rechercher des produits');
  }

  const menuButton = document.querySelector('.right i');
  if (menuButton) {
    menuButton.setAttribute('aria-label', 'Ouvrir le menu de navigation');
    menuButton.setAttribute('role', 'button');
    menuButton.setAttribute('tabindex', '0');
  }

  const closeButton = document.querySelector('#exit');
  if (closeButton) {
    closeButton.setAttribute('aria-label', 'Fermer le menu');
    closeButton.setAttribute('role', 'button');
    closeButton.setAttribute('tabindex', '0');
  }

  // Add product card accessibility
  const products = document.querySelectorAll('.product');
  products.forEach((product, index) => {
    const link = product.querySelector('a');
    const title = product.querySelector('.product-title');
    const price = product.querySelector('.product-price');

    if (link && title && price) {
      link.setAttribute('aria-label', `Acheter ${title.textContent} pour ${price.textContent}`);
    }

    product.setAttribute('role', 'article');
    product.setAttribute('aria-labelledby', `product-title-${index}`);

    if (title) {
      title.id = `product-title-${index}`;
    }
  });

  // Add form accessibility
  const formInputs = document.querySelectorAll('input, select, textarea');
  formInputs.forEach(input => {
    const label = document.querySelector(`label[for="${input.id}"]`);
    if (!label && input.placeholder) {
      input.setAttribute('aria-label', input.placeholder);
    }

    if (input.hasAttribute('required')) {
      input.setAttribute('aria-required', 'true');
      const parentLabel = input.closest('.form-group')?.querySelector('label');
      if (parentLabel) {
        parentLabel.classList.add('required');
      }
    }
  });

  // Add live region for dynamic content
  const liveRegion = document.createElement('div');
  liveRegion.setAttribute('aria-live', 'polite');
  liveRegion.setAttribute('aria-atomic', 'true');
  liveRegion.className = 'live-region';
  liveRegion.id = 'live-region';
  document.body.appendChild(liveRegion);

  // Announce page changes
  function announcePageChange(message) {
    const liveRegion = document.getElementById('live-region');
    if (liveRegion) {
      liveRegion.textContent = message;
      setTimeout(() => {
        liveRegion.textContent = '';
      }, 1000);
    }
  }

  // Add focus management for sidebar
  const originalOpenSidebar = window.openSidebar;
  window.openSidebar = function() {
    originalOpenSidebar();
    // Focus the close button when sidebar opens
    setTimeout(() => {
      closeButton?.focus();
    }, 100);
    announcePageChange('Menu de navigation ouvert');
  };

  const originalCloseSidebar = window.closeSidebar;
  window.closeSidebar = function() {
    originalCloseSidebar();
    // Return focus to menu button
    menuButton?.focus();
    announcePageChange('Menu de navigation fermé');
  };

  // Add high contrast mode detection
  if (window.matchMedia('(prefers-contrast: high)').matches) {
    document.body.classList.add('high-contrast');
  }

  // Add reduced motion detection
  if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
    document.body.classList.add('reduced-motion');
  }

  // Improve form error announcements
  const originalShowNotification = window.showNotification;
  if (originalShowNotification) {
    window.showNotification = function(message, type) {
      originalShowNotification(message, type);
      announcePageChange(message);
    };
  }
}

// Initialize accessibility features
document.addEventListener('DOMContentLoaded', initAccessibilityFeatures);

let input = document.querySelector(".form input:first-of-type");
function scrollToTop() {
  input.focus();
  const scrollDuration = 500;
  const scrollStep = -window.scrollY / (scrollDuration / 15);
  const scrollInterval = setInterval(() => {
    if (window.scrollY !== 0) {
      window.scrollBy(0, scrollStep);
    } else {
      clearInterval(scrollInterval);
    }
  }, 15);
}


document.getElementById("phoneNumber").addEventListener("input", function() {
  if (this.value.length > 10) {
      this.value = this.value.slice(0, 10);
  }
});


//////////////////////////////////////

document.addEventListener('DOMContentLoaded', function () {
  let currentIndex = 0;
  const mainPic = document.getElementById('main-pic');
  const pictures = document.querySelectorAll('#slider .pic');
  const totalPictures = pictures.length + 1;

  function updateMainPic(index) {
    if (index === 0) {
      mainPic.style.display = 'block';
    } else {
      mainPic.style.display = 'none';
    }

    pictures.forEach((pic, i) => {
      pic.style.display = i === index - 1 ? 'block' : 'none';
    });
  }

  document.getElementById('prev').addEventListener('click', function () {
    currentIndex = (currentIndex - 1 + totalPictures) % totalPictures;
    updateMainPic(currentIndex);
  });

  document.getElementById('next').addEventListener('click', function () {
    currentIndex = (currentIndex + 1) % totalPictures;
    updateMainPic(currentIndex);
  });

  // Initialize the first picture as active
  updateMainPic(currentIndex);
});


