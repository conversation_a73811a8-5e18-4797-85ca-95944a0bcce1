<?php
session_start();
$_SESSION['confirmation_completed'] = true;
?>

<!DOCTYPE html>
<html lang="fr">

<head>
  <meta charset="UTF-8" />
      <link rel="icon" href="icon.gif" type="image/x-icon">

  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200..1000&display=swap" rel="stylesheet" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <link rel="stylesheet" href="hide.css" />
  <link rel="stylesheet" href="all.min.css" />
  <title>Congratulation</title>
</head>

<style>
  body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f5f5f5;
  }

  .container {
    min-height: 600px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    text-align: center;
  }

  h1,
  h4 {
    color: #333;
    margin-bottom: 10px;
  }

  p {
    color: #666;
    line-height: 1.6;
  }

  .btn {
    display: inline-block;
    padding: 10px 20px;
    background-color: #007bff;
    color: #fff;
    text-decoration: none;
    border-radius: 5px;
    transition: background-color 0.3s ease;
  }

  .btn:hover {
    background-color: #0056b3;
  }

  .image {
    background-image: url(balloon.webp);
    background-size: contain;
    background-position: center;
    width: 100%;
    height: 400px;
    background-repeat: no-repeat;
    margin: 10px auto;
  }

  #a {
    text-decoration: none;
    color: white;
    font-weight: bold;
    background-color: rgba(76, 0, 255, 0.511);
    padding: 20px;
    margin: 20px auto;
    border-radius: 10px;

  }


  .vide {
    padding: 20px;
  }
</style>

<body>
<?php
  include("Connecte.php");
  $sqlpixel = "SELECT script FROM facebook_pixel ";
  $resultpixel = $conn->query($sqlpixel);

  if ($resultpixel->num_rows > 0) {
    $rowpixel = $resultpixel->fetch_assoc();
    $pixel_id = $rowpixel['script'];
  }
  ?>
  <script>
    ! function(f, b, e, v, n, t, s) {
      if (f.fbq) return;
      n = f.fbq = function() {
        n.callMethod ?
          n.callMethod.apply(n, arguments) : n.queue.push(arguments)
      };
      if (!f._fbq) f._fbq = n;
      n.push = n;
      n.loaded = !0;
      n.version = '2.0';
      n.queue = [];
      t = b.createElement(e);
      t.async = !0;
      t.src = v;
      s = b.getElementsByTagName(e)[0];
      s.parentNode.insertBefore(t, s)
    }(window, document, 'script',
      'https://connect.facebook.net/en_US/fbevents.js');
    fbq('init', '<?php echo $pixel_id; ?>');
    fbq('track', 'PageView');
  </script>
  <noscript>
    <img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=<?php echo $pixel_id; ?>&ev=PageView&noscript=1" />
  </noscript>
  <div class="container">
    <h1> ! شكرا على طلبكم</h1>
    <h4>سيتم الإتصال بكم من طرف فريق الدعم الخاص بنا من أجل تأكيد الطلب ولإجابة على جميع أسئلتكم وبعدها سيتم توصيل المنتج إليكم</h4>
    <p>   شكرا على ثقتك ابقى علي اتصال</p>
    <div class="image"></div>
    <a id="a" href="index.php">Retour à la page principale</a>
    <div class="vide"></div>
    <br>
  </div>
</body>
<script src="page.js"></script>

</html>